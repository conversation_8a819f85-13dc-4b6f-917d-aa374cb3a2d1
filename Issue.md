  
 in  staking.move 
  
Weak validator list validation in do_stake

Severity: Low
Where: public(friend) fun do_stake(..., validators: vector<address>, ...)
Why: You compute active_validators but don’t verify the provided validators are all active. A faulty operator list can cause stake attempts to revert.
Remediation: Filter/verify: assert!(is_active_validator(v, &active_validators), EValidatorNotFound) or silently skip inactive.