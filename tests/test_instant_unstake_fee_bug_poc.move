#[test_only]
module haedal::test_instant_unstake_fee_bug_poc {
    use sui::coin;
    use sui::test_scenario::{Self};
    use sui::test_utils::{assert_eq};
    use sui_system::sui_system::{SuiSystemState};
    use std::debug;

    use haedal::staking;
    use haedal::config;
    use haedal::haedal_test::{Self, assert_gt};

    const TEST_DEPLOYER: address = @0x1337;
    const TEST_STAKER_1: address = @0x42;
    const MIST_PER_SUI: u64 = 1_000_000_000;

    /// Test to verify the fee-free dust bug in instant unstaking
    /// This POC demonstrates that users can unstake small amounts without paying service fees
    /// due to integer division rounding down to zero
    #[test]
    fun test_instant_unstake_fee_free_dust_bug() {
        // Setup sui system
        haedal_test::set_up_sui_system_state_with_storage_fund();

        // Setup haedal staking system
        let (scenario_val, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_val;

        // First, let's stake a larger amount to have liquidity in the pool
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let large_stake_amount = 100 * MIST_PER_SUI; // 100 SUI
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        let large_hasui = staking::request_stake_coin(
            &mut system_state,
            &mut staking_object,
            coin::mint_for_testing(large_stake_amount, test_scenario::ctx(scenario)),
            @0x0,
            test_scenario::ctx(scenario)
        );
        test_scenario::return_shared(system_state);

        // Record initial service fee vault amount
        let initial_service_fee_amount = staking::get_service_sui_vault_amount(&staking_object);
        debug::print(&b"Initial service fee vault amount:");
        debug::print(&initial_service_fee_amount);

        // Get the current service fee rate (should be DEFAULT_SERVICE_FEE_RATE = 90_0000 = 0.9%)
        let service_fee_rate = config::get_service_fee(staking::get_config_mut(&mut staking_object));
        debug::print(&b"Service fee rate:");
        debug::print(&service_fee_rate);
        assert_gt(service_fee_rate, 0); // Ensure service fee is configured

        // Test Case: Try to unstake a very small amount that should trigger the bug
        // Calculate a small amount that will result in fee_amount = 0
        // fee_amount = (amount * service_fee) / FEE_DENOMINATOR
        // We want: (amount * 90_0000) / 1000_0000 < 1
        // So: amount < 1000_0000 / 90_0000 = ~11.11
        // Let's use 10 MIST (0.00000001 SUI)
        let small_unstake_amount = 10; // 10 MIST - very small amount
        let small_hasui = coin::split(&mut large_hasui, small_unstake_amount, test_scenario::ctx(scenario));

        debug::print(&b"Attempting to unstake small amount:");
        debug::print(&small_unstake_amount);

        // Calculate expected fee (this should be 0 due to rounding)
        let fee_denominator = 1000_0000u128;
        let expected_fee = ((small_unstake_amount as u128) * (service_fee_rate as u128) / fee_denominator as u64);
        debug::print(&b"Expected fee amount (should be 0 due to rounding):");
        debug::print(&expected_fee);
        assert_eq(expected_fee, 0); // This proves the bug - fee rounds to 0

        // Perform instant unstake
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        let unstaked_sui = staking::request_unstake_instant_coin(
            &mut system_state,
            &mut staking_object,
            small_hasui,
            test_scenario::ctx(scenario)
        );
        test_scenario::return_shared(system_state);

        // Check that we received SUI back
        let received_sui_amount = coin::value(&unstaked_sui);
        debug::print(&b"Received SUI amount:");
        debug::print(&received_sui_amount);
        assert_gt(received_sui_amount, 0); // We should receive some SUI back

        // Check service fee vault - it should be unchanged (proving the bug)
        let final_service_fee_amount = staking::get_service_sui_vault_amount(&staking_object);
        debug::print(&b"Final service fee vault amount:");
        debug::print(&final_service_fee_amount);

        // BUG VERIFICATION: Service fee vault should have increased, but it didn't!
        assert_eq(initial_service_fee_amount, final_service_fee_amount); // This proves the bug

        debug::print(&b"BUG CONFIRMED: User unstaked without paying any service fee!");

        // Clean up
        coin::burn_for_testing(unstaked_sui);
        coin::burn_for_testing(large_hasui);

        // Cleanup
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        haedal_test::haedal_test_tear_down(scenario_val, staking_object, admin_cap, clock_object);
    }

    /// Test to demonstrate the fix - what should happen when the assert is restored
    #[test]
    #[expected_failure(abort_code = 9)] // EUnstakeInstantNoServiceFee
    fun test_instant_unstake_with_restored_assert_should_fail() {
        // This test would pass if we restore the commented assert
        // For now, it will fail because the assert is commented out
        
        // Setup
        haedal_test::set_up_sui_system_state_with_storage_fund();
        let (scenario_val, staking_object, admin_cap, clock_object) = haedal_test::haedal_test_setup(TEST_DEPLOYER);
        let scenario = &mut scenario_val;

        // Stake some amount first
        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let stake_amount = 100 * MIST_PER_SUI;
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        let hasui = staking::request_stake_coin(
            &mut system_state, 
            &mut staking_object, 
            coin::mint_for_testing(stake_amount, test_scenario::ctx(scenario)), 
            @0x0, 
            test_scenario::ctx(scenario)
        );
        test_scenario::return_shared(system_state);

        // Try to unstake small amount that would result in 0 fee
        let small_amount = 10; // This should result in 0 fee
        let small_hasui = coin::split(&mut hasui, small_amount, test_scenario::ctx(scenario));

        test_scenario::next_tx(scenario, TEST_STAKER_1);
        let system_state = test_scenario::take_shared<SuiSystemState>(scenario);
        
        // This should fail with EUnstakeInstantNoServiceFee if the assert was restored
        let unstaked_sui = staking::request_unstake_instant_coin(
            &mut system_state,
            &mut staking_object,
            small_hasui,
            test_scenario::ctx(scenario)
        );

        test_scenario::return_shared(system_state);
        coin::burn_for_testing(hasui);
        coin::burn_for_testing(unstaked_sui);
        
        // Cleanup
        test_scenario::next_tx(scenario, TEST_DEPLOYER);
        haedal_test::haedal_test_tear_down(scenario_val, staking_object, admin_cap, clock_object);
    }
}
